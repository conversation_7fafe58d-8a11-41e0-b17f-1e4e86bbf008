// You can write more code here

/* START OF COMPILED CODE */

import { Bullet, BulletType } from "@/entities/Bullet";
import { GameConfig } from "../config/GameConfig";
import { ResolutionManager } from "./ResolutionManager";

class BulletManager {
  private scene: Phaser.Scene;
  private playerBullets: Phaser.GameObjects.Group;
  private enemyBullets: Phaser.GameObjects.Group;

  // World bounds (using virtual coordinates)
  private WORLD_WIDTH: number;
  private WORLD_HEIGHT: number;
  private BULLET_BUFFER: number;

  // Resolution management
  private gameConfig: GameConfig;
  private resolutionManager: ResolutionManager;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;

    // Initialize resolution management
    this.gameConfig = GameConfig.getInstance();
    this.resolutionManager = ResolutionManager.getInstance();

    // Get world constants from virtual resolution
    const virtualRes = this.resolutionManager.getVirtualResolution();
    this.WORLD_WIDTH = virtualRes.width;
    this.WORLD_HEIGHT = virtualRes.height;
    this.BULLET_BUFFER = 12; // Scaled for virtual resolution

    // Create groups for bullet management (Matter.js doesn't use physics groups the same way)
    this.playerBullets = scene.add.group({
      classType: Bullet,
      maxSize: 200,
      runChildUpdate: false,
    });

    this.enemyBullets = scene.add.group({
      classType: Bullet,
      maxSize: 200,
      runChildUpdate: false,
    });
  }

  public fireBullet(
    x: number,
    y: number,
    rotation: number,
    bulletType: BulletType
  ): void {
    const group =
      bulletType === BulletType.Player ? this.playerBullets : this.enemyBullets;

    // Get a bullet from the pool
    const bullet = group.get(x, y) as Bullet;

    if (bullet) {
      // Add to Matter physics world if not already added
      if (!(bullet as any).body) {
        this.scene.matter.add.gameObject(bullet);
      }

      // Fire the bullet (this will activate, position, and set velocity)
      bullet.fire(x, y, rotation, bulletType);
    }
  }

  public update(time: number, delta: number): void {
    // Check for off-screen bullets and recycle them
    [this.playerBullets, this.enemyBullets].forEach((group) => {
      group.children.forEach((child) => {
        const bullet = child as Bullet;
        if (bullet.active && bullet.visible && bullet.isOffScreen()) {
          group.killAndHide(bullet);
          (bullet as any).body.enable = false; // Disable physics body to stop updates
        }
        return true;
      });
    });
  }

  public getPlayerBulletGroup(): Phaser.GameObjects.Group {
    return this.playerBullets;
  }

  public getEnemyBulletGroup(): Phaser.GameObjects.Group {
    return this.enemyBullets;
  }

  public getPlayerBullets(): Bullet[] {
    return this.playerBullets
      .getChildren()
      .filter((child) => child.active) as Bullet[];
  }

  public getEnemyBullets(): Bullet[] {
    return this.enemyBullets
      .getChildren()
      .filter((child) => child.active) as Bullet[];
  }

  public destroyBullet(bullet: Bullet): void {
    // Immediately mark as inactive to prevent multiple collision processing
    bullet.setActive(false);
    bullet.setVisible(false);

    const group =
      bullet.getBulletType() === BulletType.Player
        ? this.playerBullets
        : this.enemyBullets;
    group.killAndHide(bullet);
    (bullet as any).body.enable = false;
  }

  public clear(): void {
    this.playerBullets.clear(true, true);
    this.enemyBullets.clear(true, true);
  }
}

export { BulletManager };
