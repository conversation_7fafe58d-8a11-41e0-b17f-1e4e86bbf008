import { Player } from "@/entities/Player";
import { InputSystem } from "@/systems/InputSystem";
import { BulletManager } from "@/systems/BulletManager";
import { ParticleManager } from "@/systems/ParticleManager";
import { Enemy } from "@/entities/Enemy";
import { Module } from "../entities/Module";
import Phaser from "phaser";
import { Bullet } from "@/entities/Bullet";
import { emit } from "@/systems/EventBus";
import { BulletType } from "@/entities/Bullet";
import { GameConfig } from "../config/GameConfig";
import { ResolutionManager } from "@/systems/ResolutionManager";
import { LevelData, EntityTile } from "@/types/LevelData";

class Level extends Phaser.Scene {
  // World constants (now using virtual coordinates)
  public readonly WORLD_WIDTH: number;
  public readonly WORLD_HEIGHT: number;
  public readonly EDGE_BUFFER: number = 12; // Scaled for virtual resolution
  public readonly REMOVE_DISTANCE: number = 50; // Scaled for virtual resolution

  // Resolution management
  private gameConfig: GameConfig;
  private resolutionManager: ResolutionManager;

  // Star field properties
  private backStars: Phaser.GameObjects.Graphics[] = [];
  private middleStars: Phaser.GameObjects.Graphics[] = [];
  private frontStars: Phaser.GameObjects.Graphics[] = [];

  // Star speeds for each layer
  private readonly BACK_STAR_SPEED = 0.2;
  private readonly MIDDLE_STAR_SPEED = 0.8;
  private readonly FRONT_STAR_SPEED = 1.5;

  // Player object
  private player: Player;
  private inputSystem: InputSystem;
  private bulletManager: BulletManager;
  private particleManager: ParticleManager;
  private modules: Module[] = [];

  // Enemy system
  private enemies: Enemy[] = [];

  // FPS debug logging
  private lastFpsLog: number = 0;
  private readonly fpsLogInterval: number = 1000; // 1 second in milliseconds

  // Level data and vertical scrolling
  private levelData: LevelData | null = null;
  private playerYPos: number = 0; // Current vertical position in level coordinates
  private readonly scrollSpeed: number = 30; // Pixels per second upward movement
  private spawnedEnemies: Set<string> = new Set(); // Track which enemies have been spawned

  constructor() {
    super("Level");

    // Initialize resolution management
    this.gameConfig = GameConfig.getInstance();
    this.resolutionManager = ResolutionManager.getInstance();

    // Set world dimensions from virtual resolution
    const virtualRes = this.resolutionManager.getVirtualResolution();
    this.WORLD_WIDTH = virtualRes.width;
    this.WORLD_HEIGHT = virtualRes.height;
  }

  create(data?: { levelData?: LevelData }) {
    // Reset level state for fresh start
    this.playerYPos = 0;
    this.spawnedEnemies.clear();
    this.enemies = [];

    // Store level data if provided
    if (data?.levelData) {
      this.levelData = data.levelData;
      console.log("Level loaded with data:", this.levelData.metadata.name);
      console.log("Entities in level:", this.levelData.entities.length);
    } else {
      console.log("Level loaded without level data - using random spawning");
    }

    // Set up cleanup on shutdown event
    this.events.once("shutdown", () => {
      this.playerYPos = 0;
      this.cleanup();
    });

    // Set up camera bounds for the target resolution
    const targetRes = this.resolutionManager.getTargetResolution();
    this.cameras.main.setBounds(0, 0, targetRes.width, targetRes.height);

    // Don't set Matter.js world bounds - we want objects to be able to move outside the screen

    // Create star field before other elements
    this.createStarField();

    // Initialize input system
    this.inputSystem = InputSystem.getInstance();
    this.inputSystem.initialize(this);

    // Create bullet manager (now handles its own groups)
    this.bulletManager = new BulletManager(this);

    // Create particle manager
    this.particleManager = new ParticleManager(this);

    // Create player positioned for vertical scrolling
    // Position player in the lower portion of the screen for vertical scrolling
    const playerX = targetRes.width / 2; // Center horizontally
    const playerY = targetRes.height * 0.8; // 80% down from top (near bottom)

    this.player = new Player(this, playerX, playerY);
    this.player.setBulletManager(this.bulletManager);

    // Set up collision detection using Matter.js collision events
    this.setupCollisions();

    this.scene.launch("Debug");
  }

  private setupCollisions(): void {
    // Set up Matter.js collision detection
    this.matter.world.on("collisionstart", (event: any) => {
      const pairs = event.pairs;

      for (let i = 0; i < pairs.length; i++) {
        const bodyA = pairs[i].bodyA;
        const bodyB = pairs[i].bodyB;
        const gameObjectA = bodyA.gameObject;
        const gameObjectB = bodyB.gameObject;

        if (!gameObjectA || !gameObjectB) continue;

        // Check for bullet-enemy collisions
        if (this.isBulletEnemyCollision(gameObjectA, gameObjectB)) {
          const bullet = this.getBulletFromPair(gameObjectA, gameObjectB);
          const enemy = this.getEnemyFromPair(gameObjectA, gameObjectB);
          if (
            bullet &&
            enemy &&
            bullet.getBulletType() === BulletType.Player &&
            bullet.active
          ) {
            this.handlePlayerBulletEnemyCollision(bullet, enemy);
          }
        }

        // Check for enemy bullet-player collisions
        if (this.isEnemyBulletPlayerCollision(gameObjectA, gameObjectB)) {
          const bullet = this.getBulletFromPair(gameObjectA, gameObjectB);
          if (
            bullet &&
            bullet.getBulletType() === BulletType.Enemy &&
            bullet.active
          ) {
            this.handleEnemyBulletPlayerCollision(this.player, bullet);
          }
        }

        // Check for enemy bullet-module collisions
        if (this.isEnemyBulletModuleCollision(gameObjectA, gameObjectB)) {
          const bullet = this.getBulletFromPair(gameObjectA, gameObjectB);
          const module = this.getModuleFromPair(gameObjectA, gameObjectB);
          if (
            bullet &&
            module &&
            bullet.getBulletType() === BulletType.Enemy &&
            bullet.active
          ) {
            this.handleEnemyBulletModuleCollision(bullet, module);
          }
        }
      }
    });
  }

  // Helper methods for collision detection
  private isBulletEnemyCollision(objA: any, objB: any): boolean {
    return (
      (objA instanceof Bullet && this.enemies.includes(objB)) ||
      (objB instanceof Bullet && this.enemies.includes(objA))
    );
  }

  private isEnemyBulletPlayerCollision(objA: any, objB: any): boolean {
    return (
      (objA instanceof Bullet && objB === this.player) ||
      (objB instanceof Bullet && objA === this.player)
    );
  }

  private isEnemyBulletModuleCollision(objA: any, objB: any): boolean {
    return (
      (objA instanceof Bullet && this.modules.includes(objB)) ||
      (objB instanceof Bullet && this.modules.includes(objA))
    );
  }

  private getBulletFromPair(objA: any, objB: any): Bullet | null {
    if (objA instanceof Bullet) return objA;
    if (objB instanceof Bullet) return objB;
    return null;
  }

  private getEnemyFromPair(objA: any, objB: any): Enemy | null {
    if (this.enemies.includes(objA)) return objA;
    if (this.enemies.includes(objB)) return objB;
    return null;
  }

  private getModuleFromPair(objA: any, objB: any): Module | null {
    if (this.modules.includes(objA)) return objA;
    if (this.modules.includes(objB)) return objB;
    return null;
  }

  private handlePlayerBulletEnemyCollision(
    bulletObj: any,
    enemyObj: any
  ): void {
    const bullet = bulletObj as Bullet;
    const enemy = enemyObj as Enemy;

    // Destroy the bullet
    this.bulletManager.destroyBullet(bullet);

    // Remove enemy from array
    const enemyIndex = this.enemies.indexOf(enemy);
    if (enemyIndex > -1) {
      this.enemies.splice(enemyIndex, 1);
    }

    // Create explosion at enemy position
    this.particleManager.createExplosion(enemy.x, enemy.y);
    emit("enemy:killed", { enemy });
    enemy.destroy();
  }

  private handleEnemyBulletPlayerCollision(_player: any, bullet: any): void {
    // Destroy the bullet
    this.bulletManager.destroyBullet(bullet);

    // Game over
    this.scene.start("GameOver");
  }

  private handleEnemyBulletModuleCollision(bullet: any, module: any): void {
    // Destroy the bullet
    this.bulletManager.destroyBullet(bullet);

    // Remove module from array
    const moduleIndex = this.modules.indexOf(module);
    if (moduleIndex > -1) {
      this.modules.splice(moduleIndex, 1);
    }

    // Create explosion at module position
    this.particleManager.createExplosion(module.x, module.y, 3);
    module.destroy();
  }

  private createStarField(): void {
    const totalStars = Phaser.Math.Between(50, 100);
    const starsPerLayer = Math.floor(totalStars / 3);

    // Create back layer stars (slowest, dimmest)
    for (let i = 0; i < starsPerLayer; i++) {
      const star = this.createStar(0.2, 0.4, 1);
      this.backStars.push(star);
    }

    // Create middle layer stars (medium speed, medium brightness)
    for (let i = 0; i < starsPerLayer; i++) {
      const star = this.createStar(0.4, 0.7, 2);
      this.middleStars.push(star);
    }

    // Create front layer stars (fastest, brightest)
    const remainingStars = totalStars - starsPerLayer * 2;
    for (let i = 0; i < remainingStars; i++) {
      const star = this.createStar(0.7, 1.0, 3);
      this.frontStars.push(star);
    }
  }

  private createStar(
    minAlpha: number,
    maxAlpha: number,
    maxSize: number
  ): Phaser.GameObjects.Graphics {
    const star = this.add.graphics();
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const size = Phaser.Math.Between(1, maxSize) * scaleFactor * 0.3; // Scale star size
    const alpha = Phaser.Math.FloatBetween(minAlpha, maxAlpha);

    star.fillStyle(0xffffff, alpha);
    star.fillCircle(0, 0, size);

    // Random position across the target screen (not virtual)
    const targetRes = this.resolutionManager.getTargetResolution();
    star.x = Phaser.Math.Between(0, targetRes.width);
    star.y = Phaser.Math.Between(0, targetRes.height);

    return star;
  }

  private updateEnemies(time: number, delta: number): void {
    const enemiesToRemove: Enemy[] = [];
    const playerPosition = this.player.getPosition();

    // Update each enemy
    this.enemies.forEach((enemy) => {
      enemy.update(time, delta, playerPosition);

      // Remove enemies that are off screen
      if (enemy.isOffScreen()) {
        enemiesToRemove.push(enemy);
      }
    });

    // Clean up off-screen enemies
    enemiesToRemove.forEach((enemy) => {
      const index = this.enemies.indexOf(enemy);
      if (index > -1) {
        this.enemies.splice(index, 1);
        enemy.destroy();
      }
    });
  }

  update(time: number, delta: number): void {
    // Update input system
    this.inputSystem.update();

    // Update bullet manager (handles recycling off-screen bullets)
    this.bulletManager.update(time, delta);

    // Update particle manager
    this.particleManager.update(delta);

    // Update player (shooting is handled internally)
    this.player.update(time, delta);
    this.modules.forEach((module) => module.update(time, delta));

    // Update enemies
    this.updateEnemies(time, delta);

    // Update vertical scrolling and spawn enemies based on level data
    this.updateVerticalScrolling(delta);
    this.updateLevelBasedSpawning();

    // FPS debug logging every second
    if (time > this.lastFpsLog + this.fpsLogInterval) {
      const fps = this.sys.game.loop.actualFps;
      const gameTime = Math.round(time);
      console.log(`Game Time: ${gameTime}ms, FPS: ${fps.toFixed(1)}`);
      this.lastFpsLog = time;
    }

    // Update star positions
    this.updateStarLayer(this.backStars, this.BACK_STAR_SPEED);
    this.updateStarLayer(this.middleStars, this.MIDDLE_STAR_SPEED);
    this.updateStarLayer(this.frontStars, this.FRONT_STAR_SPEED);
  }

  private updateStarLayer(
    stars: Phaser.GameObjects.Graphics[],
    speed: number
  ): void {
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const targetRes = this.resolutionManager.getTargetResolution();
    const scaledSpeed = speed * scaleFactor; // Scale movement speed

    stars.forEach((star) => {
      star.y += scaledSpeed;

      // Wrap around when star goes off the bottom edge (using target resolution)
      if (star.y > targetRes.height + 10 * scaleFactor) {
        star.y = -10 * scaleFactor;
        star.x = Phaser.Math.Between(0, targetRes.width);
      }
    });
  }

  private cleanup() {
    // Remove debug scene
    this.scene.stop("Debug");

    // Clean up player
    if (this.player) {
      this.player.destroy();
    }

    // Clean up star fields
    if (this.backStars) {
      this.backStars.forEach((star) => star.destroy());
      this.backStars = [];
    }
    if (this.middleStars) {
      this.middleStars.forEach((star) => star.destroy());
      this.middleStars = [];
    }
    if (this.frontStars) {
      this.frontStars.forEach((star) => star.destroy());
      this.frontStars = [];
    }

    // Clean up input system
    if (this.inputSystem) {
      this.inputSystem.destroy();
    }

    // Clean up bullet manager
    if (this.bulletManager) {
      this.bulletManager.clear();
    }

    // Clean up particle manager
    if (this.particleManager) {
      this.particleManager.destroy();
    }

    // Clean up enemies
    if (this.enemies) {
      this.enemies.forEach((enemy) => enemy.destroy());
      this.enemies = [];
    }

    // Clean up modules
    if (this.modules) {
      this.modules.forEach((module) => module.destroy());
      this.modules = [];
    }

    // Reset level state
    this.playerYPos = 0;
    this.spawnedEnemies.clear();
  }

  shutdown() {
    this.cleanup();
  }

  destroy() {
    this.cleanup();
  }

  public getModuleCount(): number {
    return this.modules.length;
  }

  private updateVerticalScrolling(delta: number): void {
    // Update player Y position (vertical scrolling)
    const deltaSeconds = delta / 1000;
    const scaleFactor = this.resolutionManager.getScaleFactor();
    this.playerYPos += this.scrollSpeed * scaleFactor * deltaSeconds;
  }

  private updateLevelBasedSpawning(): void {
    // Only spawn enemies if we have level data
    if (!this.levelData) {
      return;
    }

    // Convert current playerYPos to level editor coordinates
    // Level editor: Y=0 at bottom (player start), Y increases upward
    // playerYPos starts at 0 and increases as we scroll up
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const tileSize = 16 * scaleFactor;
    const currentLevelY = Math.floor(this.playerYPos / tileSize);

    // At start (playerYPos=0, currentLevelY=0), we should see enemies at Y=14 and below
    // As we scroll up, we should spawn enemies that are ahead of our current position
    const spawnRangeAhead = 5; // tiles ahead to check for spawning
    const visibleRangeBelow = 15; // tiles below current position that are visible

    this.levelData.entities.forEach((entity) => {
      const entityKey = `${entity.x},${entity.y}`;

      // Skip if already spawned
      if (this.spawnedEnemies.has(entityKey)) {
        return;
      }

      // Spawn enemies that are within the visible range or just ahead
      // entity.y is the level editor Y coordinate (0 = bottom, increases upward)
      // We want to spawn enemies when they're about to become visible from the top
      const shouldSpawn =
        entity.y >= currentLevelY - visibleRangeBelow &&
        entity.y <= currentLevelY + spawnRangeAhead;

      if (shouldSpawn) {
        this.spawnLevelEnemy(entity);
        this.spawnedEnemies.add(entityKey);
        console.log(
          `Spawning enemy at level Y=${entity.y}, current player level Y=${currentLevelY}`
        );
      }
    });
  }

  private spawnLevelEnemy(entityTile: EntityTile): void {
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const tileSize = 16 * scaleFactor;

    // Convert level editor coordinates to screen coordinates
    // Level editor: X,Y in tiles, Y=0 at bottom
    // Screen: X,Y in pixels, Y=0 at top
    const screenX = entityTile.x * tileSize + tileSize / 2; // Center of tile
    const screenY = -tileSize; // Spawn just above screen

    // Create enemy configuration based on tier
    const enemyConfig = {
      color:
        entityTile.tier === 1
          ? 0xff0000
          : entityTile.tier === 2
          ? 0x00ff00
          : 0x0000ff,
      maxHealth: entityTile.tier,
      scoreValue: 100 * entityTile.tier,
    };

    // Create enemy
    const enemy = new Enemy(
      this,
      screenX,
      screenY,
      enemyConfig,
      this.bulletManager
    );

    // Set particle manager reference
    enemy.setParticleManager(this.particleManager);

    // Add to enemy array
    this.enemies.push(enemy);

    console.log(
      `Spawned ${entityTile.type} (tier ${entityTile.tier}) at level position (${entityTile.x}, ${entityTile.y})`
    );
  }

  public addModule(offsetX: number, offsetY: number): void {
    if (this.modules.length >= 4) return;
    const module = new Module(this, this.player, offsetX, offsetY);
    module.setBulletManager(this.bulletManager);
    this.modules.push(module);
    emit("module:added", { module });
  }

  public removeLastModule(): void {
    if (this.modules.length > 0) {
      const module = this.modules.pop();
      if (module) {
        emit("module:removed", { module });
        module.destroy();
      }
    }
  }
}

export { Level };
